# @smithy/fetch-http-handler

[![NPM version](https://img.shields.io/npm/v/@smithy/fetch-http-handler/latest.svg)](https://www.npmjs.com/package/@smithy/fetch-http-handler)
[![NPM downloads](https://img.shields.io/npm/dm/@smithy/fetch-http-handler.svg)](https://www.npmjs.com/package/@smithy/fetch-http-handler)

This is the default `requestHandler` used for browser applications.
Since Node.js introduced experimental Web Streams API in v16.5.0 and made it stable in v21.0.0,
you can consider using `fetch-http-handler` in Node.js, although it's not recommended.

For the Node.js default `requestHandler` implementation, see instead
[`@smithy/node-http-handler`](https://www.npmjs.com/package/@smithy/node-http-handler).
